package com.challanty.android.kp3.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntOffsetAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.challanty.android.kp3.viewModel.TileModel

/**
 * GameBoardTile that uses TileModel for more efficient recomposition.
 * This version only recomposes when the specific tile's properties change.
 */
@Composable
fun GameBoardTile(
    modifier: Modifier = Modifier,
    tileModel: TileModel,
    tileWidthDp: Dp,
    tileHeightDp: Dp,
    onTileClick: (TileModel) -> Unit,
    onTileDoubleClick: (TileModel) -> Unit,
) {
    // Animate rotation and offset based on tileModel values
    // These will only trigger recomposition when the specific property changes
    // Note: We can't mod the rotation targetValue by 360 because the animation
    // will rotate counterclockwise instead of clockwise when the value goes from
    // 270 to 0.
    val animatedRotation by animateFloatAsState(
        targetValue = tileModel.quarterTurnCnt * 90f,
        animationSpec = tween(durationMillis = tileModel.rotationDuration),
        label = "tileRotate_${tileModel.id}"
    )
    val animatedOffset by animateIntOffsetAsState(
        targetValue = tileModel.intOffset,
        animationSpec = tween(durationMillis = tileModel.intOffsetDuration),
        label = "tileMove_${tileModel.id}"
    )

    val density = LocalDensity.current
    val offsetXDp = with(density) { animatedOffset.x.toDp() }
    val offsetYDp = with(density) { animatedOffset.y.toDp() }

    Box(
        modifier = modifier
            .offset(x = offsetXDp, y = offsetYDp)
            .size(tileWidthDp, tileHeightDp)
            .background(Color.Transparent)
            .border(1.dp, Color.Gray)
            // Note: The offset modifier is relative to the cell's *original* layout position.
            .rotate(animatedRotation) // Apply animated rotation
    ) {
        Image(
            bitmap = tileModel.bitmap,
            contentDescription = "Game Cell ${tileModel.id}", // Accessibility
        )

        // Bottom Overlay
        if (tileModel.isLocked) {
            LockOverlay()
        }

        // Top Overlay
        SelectionOverlay(
            tileModel = tileModel,
            showBorder = tileModel.isSelected,
            onClick = onTileClick,
            onDoubleClick = onTileDoubleClick
        )
    }
}