package com.challanty.android.kp3.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.  extended.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.challanty.android.kp3.R
import com.challanty.android.kp3.navigation.NavRoutes

/**
 * Help screen.
 * This screen provides help and instructions for the game.
 *
 * @param onNavigate Callback for when a navigation item is clicked.
 * @param onBackPressed Callback for when the back button is pressed.
 */
@Composable
fun HelpScreen(
    onNavigate: (String) -> Unit,
    onBackPressed: () -> Unit = {}
) {
    // Handle back button press to exit the app
    BackHandler { onBackPressed() }

    BaseScreen(
        title = stringResource(R.string.screen_title_help),
        currentRoute = NavRoutes.Help.route,
        onNavigate = onNavigate
    ) {
        val cardTitles = listOf(
            stringResource(R.string.help_title_1),
            stringResource(R.string.help_title_2),
            stringResource(R.string.help_title_3),
            stringResource(R.string.help_title_4),
            stringResource(R.string.help_title_5),
            stringResource(R.string.help_title_6),
            stringResource(R.string.help_title_7),
            stringResource(R.string.help_title_8),
            stringResource(R.string.help_title_9),
            stringResource(R.string.help_title_10),
            stringResource(R.string.help_title_11),
            stringResource(R.string.help_title_12),
            stringResource(R.string.help_title_13),
            stringResource(R.string.help_title_14),
            stringResource(R.string.help_title_15),
            stringResource(R.string.help_title_16),
            stringResource(R.string.help_title_17),
            stringResource(R.string.help_title_18),
            stringResource(R.string.help_title_19),
        )
        val cardBodies = listOf(
            stringResource(R.string.help_text_1),
            stringResource(R.string.help_text_2),
            stringResource(R.string.help_text_3),
            stringResource(R.string.help_text_4),
            stringResource(R.string.help_text_5),
            stringResource(R.string.help_text_6),
            stringResource(R.string.help_text_7),
            stringResource(R.string.help_text_8),
            stringResource(R.string.help_text_9),
            stringResource(R.string.help_text_10),
            stringResource(R.string.help_text_11),
            stringResource(R.string.help_text_12),
            stringResource(R.string.help_text_13),
            stringResource(R.string.help_text_14),
            stringResource(R.string.help_text_15),
            stringResource(R.string.help_text_16),
            stringResource(R.string.help_text_17),
            stringResource(R.string.help_text_18),
            stringResource(R.string.help_text_19),
        )
        val expandedStates = remember {
            mutableListOf(*(Array(cardTitles.size) { mutableStateOf(false) }))
        }

        // NOTE: Can't use modifier.width(IntrinsicSize.Max) with LazyColumn
        LazyColumn {
            items(cardTitles) { cardTitle ->
                Card(
                    modifier = Modifier
                        .padding(8.dp),
                    elevation = 8.dp,
                    shape = RoundedCornerShape(16.dp)
                ) {
                    Column(
                        modifier = Modifier.clickable {
                            expandedStates[cardTitles.indexOf(cardTitle)].value =
                                !expandedStates[cardTitles.indexOf(cardTitle)].value
                        },
                    ) {
                        Row {
                            androidx.compose.material.Text(
                                text = cardTitle,
                                modifier = Modifier.weight(1f).padding(start = 16.dp),
                                style = MaterialTheme.typography.h5,
                            )
                            Icon(
                                imageVector = if (expandedStates[cardTitles.indexOf(cardTitle)].value) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                contentDescription = if (expandedStates[cardTitles.indexOf(cardTitle)].value) stringResource(R.string.collapse) else stringResource(R.string.expand),
                                modifier = Modifier.size(24.dp).weight(.1f),
                                tint = Color.Black,
                            )
                        }
                        if (expandedStates[cardTitles.indexOf(cardTitle)].value) {
                            androidx.compose.material.Text(
                                text = cardBodies[cardTitles.indexOf(cardTitle)],
                                modifier = Modifier.padding(8.dp),
                                style = MaterialTheme.typography.body1
                            )
                        }
                    }
                }
            }
        }
    }
}
