package com.challanty.android.kp3.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import com.challanty.android.kp3.viewModel.TileModel

@Composable
fun SelectionOverlay(
    tileModel: TileModel,
    showBorder: Boolean,
    onClick: (TileModel) -> Unit,
    onDoubleClick: (TileModel) -> Unit,
    modifier: Modifier = Modifier
) {
    // Use CombinedClickable for single and double taps
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Transparent) // Transparent background
            .then( // Apply border conditionally
                // TODO What Material3 color should we use for the border?
                if (showBorder) Modifier.border(5.dp, Color.Red) // Example border
                else Modifier
            )
            // Use pointerInput for combined clicks
            .pointerInput(tileModel) { // Key on cellId if callbacks depend on it
                detectTapGestures(
                    onTap = { onClick(tileModel) },
                    onDoubleTap = { onDoubleClick(tileModel) },
                    onLongPress = { onDoubleClick(tileModel) }
                )
            }
    ) {
        // No content inside the selection overlay itself, just handles interaction and border
    }
}